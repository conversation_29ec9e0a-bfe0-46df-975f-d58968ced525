{"name": "xendit-copilot", "displayName": "Xendit Copilot", "description": "VS Code extension that integrates with GitHub Copilot Chat to provide custom context from synced GitHub repositories with intelligent rule retrieval", "version": "0.0.4", "engines": {"vscode": "^1.100.0"}, "repository": {"type": "git", "url": "https://github.com/ai-driven-dev/rules"}, "categories": ["Other"], "activationEvents": [], "main": "./out/extension.js", "contributes": {"chatParticipants": [{"id": "chat.xendit-copilot", "name": "Xendit Copilot", "description": "AI assistant for Xendit's central knowledge repository"}], "languageModelTools": [{"name": "xendit-copilot_contextRetrieval", "displayName": "Add Context", "toolReferenceName": "addContext", "canBeReferencedInPrompt": true, "modelDescription": "Retrieve relevant context and knowledge from a central repository to help answer questions and provide information", "tags": ["knowledge", "context", "xendit-copilot", "chat-tools-sample"], "inputSchema": {"type": "object", "properties": {"query": {"type": "string", "description": "User query for context retrieval (e.g., 'payment processing', 'user authentication', 'API documentation')"}, "maxResults": {"type": "number", "description": "Maximum number of results to retrieve (default: 5)", "minimum": 1, "maximum": 20}, "service": {"type": "string", "description": "Specific service to search in (optional)"}, "includeContent": {"type": "boolean", "description": "Include full content in response (default: true)"}}, "required": ["query"]}}, {"name": "chat-tools-sample_findFiles", "tags": ["files", "search", "chat-tools-sample"], "toolReferenceName": "findFiles", "displayName": "Find Files", "modelDescription": "Search for files in the current workspace", "canBeReferencedInPrompt": true, "inputSchema": {"type": "object", "properties": {"pattern": {"type": "string", "description": "Search for files that match this glob pattern"}}, "required": ["pattern"]}}], "commands": [{"command": "xendit.setRepository", "title": "Xendit Copilot: Set Repository", "icon": "$(repo)"}, {"command": "xendit.refresh", "title": "Xendit Copilot: Refresh", "icon": "$(refresh)"}, {"command": "xendit.toggleSelection", "title": "Xendit Copilot: Toggle Selection", "icon": "$(check)"}, {"command": "xendit.downloadSelected", "title": "Xendit Copilot: Download Selected Files", "icon": "$(cloud-download)"}, {"command": "xendit.welcome", "title": "Xendit Copilot: Show Welcome Message"}, {"command": "xendit.openSettings", "title": "Xendit Copilot: Open Settings", "icon": "$(gear)"}, {"command": "xendit.clearStorage", "title": "Xendit Copilot: Clear Storage", "icon": "$(trash)"}, {"command": "xendit.refreshRuleStatus", "title": "Xendit Copilot: Check for Updates", "icon": "$(sync)"}], "viewsContainers": {"activitybar": [{"id": "ai-driven-dev-rules", "title": "Xendit Copilot", "icon": "$(github)"}]}, "views": {"ai-driven-dev-rules": [{"id": "xendit.welcomeView", "name": "Get Started", "type": "webview", "visibility": "visible"}, {"id": "ai-driven-dev-rules", "name": "Repository Explorer", "icon": "$(github)", "contextualTitle": "Xendit Copilot"}]}, "menus": {"view/title": [{"command": "xendit.setRepository", "when": "view == ai-driven-dev-rules", "group": "navigation"}, {"command": "xendit.refresh", "when": "view == ai-driven-dev-rules", "group": "navigation"}, {"command": "xendit.downloadSelected", "when": "view == ai-driven-dev-rules", "group": "navigation"}, {"command": "xendit.refreshRuleStatus", "when": "view == ai-driven-dev-rules", "group": "navigation@3"}, {"command": "xendit.openSettings", "when": "view == ai-driven-dev-rules", "group": "navigation@5"}], "view/item/context": [{"command": "xendit.toggleSelection", "when": "view == ai-driven-dev-rules", "group": "inline"}]}, "configuration": {"title": "Xendit Copilot", "properties": {"xendit.githubToken": {"type": "string", "default": "", "description": "Token d'accès personnel (PAT) GitHub. Optionnel, mais recommandé pour augmenter les limites de taux de l'API: https://github.com/settings/personal-access-tokens", "scope": "machine-overridable"}, "xendit.maxRecentRepositories": {"type": "number", "default": 5, "description": "Nombre maximum de dépôts (autres que le dépôt 'Featured') à mémoriser dans la liste de sélection rapide.", "minimum": 1, "scope": "window"}, "xendit.maxConcurrentDownloads": {"type": "number", "default": 3, "description": "Nombre maximum de fichiers à télécharger simultanément.", "minimum": 1, "scope": "window"}, "xendit.showWelcomeOnStartup": {"type": "boolean", "default": true, "description": "Afficher la vue 'Get Started' au démarrage de VS Code.", "scope": "window"}, "xendit.autoRefreshInterval": {"type": ["number", "null"], "default": null, "description": "Intervalle (en secondes) pour rafraîchir automatiquement l'explorateur de dépôt. Mettre à 'null' pour désactiver.", "minimum": 10, "scope": "window"}, "xendit.includePaths": {"type": "string", "default": ".cursor,.clinerules", "description": "Liste de chemins ou noms de fichiers/dossiers à inclure dans l'explorateur, séparés par des virgules. Si vide, tout est inclus. Ex: .cursor,docs/,README.md", "scope": "window"}}}}, "scripts": {"vscode:package": "npx vsce package", "vscode:prepublish": "tsc -p ./", "watch": "tsc -watch -p ./", "typecheck": "tsc --noEmit", "test": "npm run test:unit && npm run test:integration", "test:unit": "mocha --ui tdd out/test/unit/**/*.test.js", "test:integration": "node out/test/runTest.js", "test:coverage": "nyc npm run test", "pretest": "npm run vscode:prepublish && npm run compile-tests", "compile-tests": "node scripts/compile-tests.js"}, "devDependencies": {"@biomejs/biome": "^1.9.4", "@types/mocha": "^10.0.10", "@types/nock": "^10.0.3", "@types/node": "20.x", "@types/sinon": "^17.0.4", "@types/vscode": "^1.100.0", "@typescript-eslint/eslint-plugin": "^8.28.0", "@typescript-eslint/parser": "^8.28.0", "@vscode/test-electron": "^2.5.2", "eslint": "^9.23.0", "glob": "^10.4.5", "lefthook": "^1.11.10", "mocha": "^11.5.0", "nock": "^14.0.4", "nyc": "^17.1.0", "sinon": "^20.0.0", "typescript": "^5.8.3"}, "packageManager": "pnpm@10.7.1+sha512.2d92c86b7928dc8284f53494fb4201f983da65f0fb4f0d40baafa5cf628fa31dae3e5968f12466f17df7e97310e30f343a648baea1b9b350685dafafffdf5808", "dependencies": {"@vscode/prompt-tsx": "^0.4.0-alpha.4"}}